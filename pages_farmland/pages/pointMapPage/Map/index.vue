<template>
  <view class="map-page-container">
    <view class="map-wrapper" :style="{ height: containerHeight + 'px' }">
      <view
        id="map-box"
        class="map-canvas"
        :direction="direction"
        :change:direction="mapbox.setMarkerDirection"
        :currentLocation="currentLocation"
        :change:currentLocation="mapbox.setCurrentLocation"
        :pointData="pointData"
        :change:pointData="mapbox.pointDataChange"
      ></view>
    </view>
    <view class="info-card-container" v-if="cardData">
      <DataCard :data="cardData" :config="cardConfig" @click="cardClick(cardData)" />
    </view>
  </view>
</template>

<script>
import DataCard from "../../pointListPage/DataCard/index.vue";
import uid from "@/tools/uid";
import "mapbox-gl/dist/mapbox-gl.css";
import Navigation from "@/tools/Navigation";

export default {
  name: "MapComponent",
  components: {
    DataCard,
  },
  props: {
    projectId: {
      type: String,
      required: true,
    },
    filterMap: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      direction: null,
      currentLocation: null,
      mapName: "construction-map",
      pointData: null,
      cardData: null,
      containerHeight: 0,
      cardConfig: {
        title: "{point_type}: {point_code}",
        columns: [
          {
            label: "预设经纬度",
            field: "{preset_longitude}, {preset_latitude}",
          },
          {
            label: "地址",
            field: "{cname}{fname}{tname}{xname}",
          },
        ],
        operations: [],
      },
    };
  },
  async mounted() {
    uni
      .createSelectorQuery()
      .select(".map-wrapper")
      .boundingClientRect((rect) => {
        console.log("rect :>> ", rect);
        this.containerHeight = rect.height;
      })
      .exec();
    this.addlisteners();
  },
  beforeDestroy() {
    this.offListeners();
  },
  methods: {
    async mapboxLoaded() {
      this.reloadData();
    },

    async reloadData() {
      try {
        let filter = [["=", "project_id", this.projectId]];

        Object.keys(this.filterMap).forEach((k) => {
          if (this.filterMap[k]) filter.push(this.filterMap[k]);
        });

        if (filter.length >= 2) filter.unshift("and");

        if (filter.length === 1) filter = filter[0];

        if (!filter.length) filter = null;

        const res = await this.$apis.formData.joinSearchFormData("sampling_task", {
          filter,
          joins: [
            {
              type: "left",
              table: "point_info",
              joinedTable: "sampling_task",
              multiJoinFields: [
                {
                  joinedField: "sampling_point_id",
                  field: "_id",
                },
              ],
            },
          ],
        });

        console.log("res :>> ", res);
        this.pointData = res.list.map((item) => {
          return {
            lon: item["point_info.preset_longitude"],
            lat: item["point_info.preset_latitude"],
            taskId: item["sampling_task._id"],
            pointId: item["sampling_task.point_code"],
          };
        });
      } catch (error) {
        console.error("Failed to reload data:", error);
      }
    },

    addlisteners() {
      uni.onCompassChange(this.compassChange);
      uni.startLocationUpdate({
        type: "wgs84",
        needFullAccuracy: true,
      });
      uni.onLocationChange(this.locationChange);
    },

    offListeners() {
      uni.offCompassChange(this.compassChange);
      uni.offLocationChange(this.locationChange);
    },

    locationChange(res) {
      this.currentLocation = {
        lonlat: [res.longitude, res.latitude],
        uid: uid(),
      };
    },

    compassChange(e) {
      this.direction = e.direction;
    },

    async navigationData({ lon, lat }) {
      Navigation.navigateTo(lon, lat);
    },

    cardClick(e) {
      Navigation.navigateTo(e.preset_longitude, e.preset_latitude);
    },

    async pointClick(e) {
      try {
        console.log("pointClick :>> ", e);
        const res = await this.$apis.formData.getFormRecords("point_info", {
          filter: ["=", "point_code", e.pointId],
        });
        this.cardData = res.list[0];
      } catch (error) {
        console.error("Failed to get point data:", error);
      }
    },
  },
};
</script>

<script module="mapbox" lang="renderjs">
import mapboxgl from 'mapbox-gl'
import { getStyle } from './style.js'
import { loadImagePromise, arrowIcon, markIcon, flagIcon ,pointImage} from './config.js'


export default {
  data() {
    return {
      map: null,
      draw: null,
      marker: null,
      containerHeight: 0,
      dkStateMap: null,
    }
  },
  mounted() {
    setTimeout(() => {
      this.init()
    }, 700)
  },
  methods: {
    init() {
      this.map = new mapboxgl.Map({
        container: 'map-box',
        // style: 'http://************/api/basemap/style/default.json',
        style: getStyle(),
        center: [99.5542385, 26.6294502],
        zoom: 6,
        touchPitch: false,
        dragRotate: false,
        touchRotate: false,
      })
      window.map = this.map
      this.map.on('load', this.mapLoad)
    },
    async mapLoad() {
      await this.initMarker()
      this.$ownerInstance.callMethod('mapboxLoaded', true)
    },
   async initMarker() {
      const el = document.createElement('img')
      el.className = 'marker'
      el.src = markIcon
      el.style.width = '40px'

      this.marker = new mapboxgl.Marker({
        element: el,
        pitchAlignment: 'map',
        rotationAlignment: 'map',
      })
        .setLngLat([99.54, 26.62])
        .addTo(this.map)
      console.log('this.marker :>> ', this.marker)

        const markImage = await loadImagePromise(this.map, pointImage)
         this.map.addImage('flagIcon', markImage)

    },

    async addLayers(e) {
      const arrowImage = await loadImagePromise(this.map, arrowIcon)
      const flagImage = await loadImagePromise(this.map, flagIcon)
      this.map.addImage('arrowIcon', arrowImage)
      this.map.addImage('flagIcon', flagImage)
    },

    setMarkerDirection(e) {
      if (!e) return
      this.marker && this.marker.setRotation(e)
    },

    setCurrentLocation(val) {
      if (!val || !val.lonlat) return
      if (!Array.isArray(val.lonlat) || val.lonlat.length !== 2) return
      if (this.marker) {
        this.marker.setLngLat(val.lonlat)
      }
    },

    pointDataChange(e) {
      if (!e) return
      if (this.map.getLayer('point')) {
        this.map.removeLayer('point')
      }
      if (this.map.getSource('point')) {
        this.map.removeSource('point')
      }
      const geoSource = {
        type: 'FeatureCollection',
        features: e.map((item) => {
          return {
            type: 'Feature',
            geometry: {
              type: 'Point',
              coordinates: [item.lon, item.lat],
            },
            properties: { ...item },
          }
        }),
      }
      console.log('geoSource :>> ', geoSource);
      this.map.addSource('point',{
         type: 'geojson',
        data: geoSource,
      })
      this.map.addLayer({
        id: 'point',
          type: 'symbol',
      source: 'point',
      paint: {},
      layout: {
        'icon-image': 'flagIcon',
        'icon-size': 0.25,
      },
      })
      this.map.on('click','point',(e)=>{
      this.$ownerInstance.callMethod('pointClick',   e.features[0].properties)
      })
    },
  },
}
</script>

<style lang="scss" scoped>
/* 地图页面主容器 */
.map-page-container {
  position: relative;
  height: 100%;
}

/* 地图包装器 */
.map-wrapper {
  position: relative;
  width: 100%;
}

/* 地图画布 */
.map-canvas {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 信息卡片容器 */
.info-card-container {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
  left: 20rpx;
  z-index: 10;
}
</style>
