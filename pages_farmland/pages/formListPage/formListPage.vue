
<template>
  <view class="page-container">
    <u-navbar
      :title="title || '数据列表'"
      :placeholder="true"
      :safe-area-inset-top="true"
      
      @leftClick="navigateBack"
    ></u-navbar>

    <SearchBar :config="searchConfig" v-if="searchConfig" @change="searchChangeHandler" />
    <view class="info-bar">
      <view>共{{ total }}条</view>
      <view class="tag-container">
        <u-tag
          :type="createByMyself ? 'primary' : 'default'"
          @click="createByMyselfHandler"
          :plain="!createByMyself"
          shape="circle"
        >
          我的填报
        </u-tag>
      </view>
    </view>
    <scroll-view class="scroll-container" scroll-y @scrolltolower="loadMoreData">
      <view class="list-content">
        <view class="card-item" v-for="item in dataList" :key="item._id">
          <DataCard
            :data="item"
            :userInfo="userInfo"
            :config="cardConfig"
            @operation="operationHandler"
          ></DataCard>
        </view>
      </view>
    </scroll-view>
    <view class="cache-badge" v-if="cacheDataCount">
      <u-badge :count="cacheDataCount">
        <u-button size="small" @click="toCacheDataList">暂存数据</u-button>
      </u-badge>
    </view>
    <view class="fab-container">
      <u-button
        type="primary"
        shape="circle"
        size="large"
        @click="addRecordHandler"
        :custom-style="fabStyle"
      >
        <u-icon name="plus" size="24" color="white"></u-icon>
      </u-button>
    </view>
  </view>
</template>

<script>
import DataCard from '@/components/common/DataCard/index.vue'
import SearchBar from '@/components/common/SearchBar/index.vue'
import { DEFAULT_CARD_CONFIG_OPERATIONS } from './list.config'

export default {
  name: 'formListPage',
  components: {
    DataCard,
    SearchBar
  },
  data() {
    return {
      formUid: null,
      pageLoaded: false,
      pageParam: {
        pageNum: 1,
        pageSize: 10,
      },
      title: null,
      cardConfig: null,
      searchConfig: null,
      dataList: [],
      filterMap: {},
      total: 0,
      cacheDataCount: 0,
      createByMyself: false,
      loading: false,
      userInfo: null,
      fabStyle: {
        position: 'fixed',
        right: '20px',
        bottom: '20px',
        width: '56px',
        height: '56px',
        borderRadius: '28px'
      }
    }
  },
  mounted() {
    // 获取用户信息，这里需要根据实际项目的用户信息获取方式调整
    this.userInfo = uni.getStorageSync('userInfo') || {}
  },
  onShow() {
    if (this.formUid && this.pageLoaded) {
      this.getCacheData()
      this.reloadData()
    }
  },
  onLoad(e) {
    this.initPage(e)
  },
  watch: {
    loading(newValue) {
      if (newValue) {
        uni.showLoading({
          title: '加载中'
        })
      } else {
        uni.hideLoading()
      }
    }
  },
  methods: {
    navigateBack() {
      uni.navigateBack()
    },
    showToast(message) {
      uni.showToast({
        title: message,
        icon: 'none'
      })
    },
    async getDataList(reload) {
      let filter = []

      Object.keys(this.filterMap).forEach((k) => {
        if (this.filterMap[k]) filter.push(this.filterMap[k])
      })

      if (filter.length >= 2) filter.unshift('and')

      if (filter.length === 1) filter = filter[0]

      if (!filter.length) filter = null

      try {
        this.loading = true
        const res = await this.$apis.formData.getFormRecords(this.formUid, { filter, page: this.pageParam })
        this.total = res.total
        if (reload) {
          this.dataList = [...res.list]
        } else {
          this.dataList.push(...res.list)
        }
      } catch (error) {
        console.error('获取数据失败:', error)
      } finally {
        this.loading = false
      }
    },
    async loadMoreData() {
      console.log('loadMoreData')
      this.pageParam.pageNum += 1
      await this.getDataList()
    },
    async reloadData() {
      this.pageParam.pageNum = 1
      await this.getDataList(true)
      this.pageLoaded = true
    },
    async operationHandler({ key, data }) {
      console.log(key, data)
      switch (key) {
        case 'edit':
          this.editRecordHandler(data)
          break
        case 'view':
          this.viewRecordHandler(data)
          break
        case 'delete':
          this.deleteRecordHandler(data)
          break
        default:
          break
      }
    },
    async searchChangeHandler(field, filter) {
      console.log(field, filter)
      this.filterMap[field] = filter
      await this.reloadData()
    },
    async createByMyselfHandler() {
      this.createByMyself = !this.createByMyself
      console.log('userInfo :>> ', this.userInfo)
      this.filterMap.createBy = this.createByMyself ? ['=', 'create_by', this.userInfo.user_id] : null
      await this.reloadData()
    },
    async getCacheData() {
      const res = await this.$apis.formDataLocal.countCacheRecords(this.formUid)
      console.log(res, 'cache')
      this.cacheDataCount = res
    },
    async toCacheDataList() {
      uni.setStorageSync(this.formUid, this.cardConfig)
      uni.navigateTo({
        url: `/pages_farmland/pages/cacheDataListPage/cacheDataListPage?formUid=${this.formUid}`,
      })
    },
    async addRecordHandler() {
      uni.navigateTo({
        url: `/pages_farmland/pages/formPage/formPage?formUid=${this.formUid}&cacheabl=false`,
      })
    },
    async editRecordHandler(record) {
      const formRecordId = record._id
      uni.setStorageSync(formRecordId, record)
      uni.navigateTo({
        url: `/pages_farmland/pages/formPage/formPage?formUid=${this.formUid}&formRecordId=${formRecordId}`,
      })
    },
    async viewRecordHandler(record) {
      const formRecordId = record._id
      uni.setStorageSync(formRecordId, record)
      uni.navigateTo({
        url: `/pages_farmland/pages/formPage/formPage?formUid=${this.formUid}&formRecordId=${formRecordId}&readonly=1`,
      })
    },
    async deleteRecordHandler(record) {
      try {
        await this.$apis.formData.deleteFormDataById(this.formUid, record._id)
        await this.reloadData()
      } catch (error) {
        this.showToast('删除失败')
      }
    },
    async initPage(e) {
      console.log(e)

      this.formUid = e.formUid
      if (e.title) {
        this.title = e.title
      }
      const conf = uni.getStorageSync(this.formUid)
      console.log(conf)

      this.cardConfig = conf.card_config
        ? JSON.parse(conf.card_config)
        : { ...DEFAULT_CARD_CONFIG_OPERATIONS }

      if (conf.search_config) {
        this.searchConfig = JSON.parse(conf.search_config)
        console.log(this.searchConfig)
      }
      await this.getCacheData()
      await this.reloadData()
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.info-bar {
  background-color: white;
  padding: 4px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.tag-container {
  display: flex;
}

.scroll-container {
  background-color: #f5f5f5;
  flex: 1;
}

.list-content {
  padding-bottom: 48px;
}

.card-item {
  margin: 12px;
}

.cache-badge {
  position: fixed;
  bottom: 40px;
  left: 12px;
}

.fab-container {
  position: fixed;
  right: 20px;
  bottom: 20px;
}
</style>
