<route lang="json5">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '数据列表',
  },
}
</route>
<template>
  <view class="h-screen bg flex flex-col">
    <wd-navbar
      :title="title || '数据列表'"
      placeholder
      safeAreaInsetTop
      leftArrow
      @leftClick="navigateBack"
    ></wd-navbar>
    <wd-tabs class="" v-model="tab">
      <wd-tab title="待填报"></wd-tab>
      <wd-tab title="已填报"></wd-tab>
    </wd-tabs>
    <view class="flex-1 overflow-hidden">
      <TaskListContent
        v-if="tab === 0"
        ref="taskListContent"
        :formUid="formUid"
        :cardConfig="cardConfig"
        :fillFormUid="fillFormUid"
        :searchConfig="searchConfig"
      />

      <FillListContent
        ref="fillListContent"
        v-if="fillFormUid && tab === 1"
        :formUid="formUid"
        :fillFormUid="fillFormUid"
        :cardConfig="fillCardConfig || cardConfig"
        :searchConfig="fillSearchConfig || searchConfig"
      />
    </view>

    <!-- <view class="offline-list">
      <wd-badge
        v-if="outlineCount"
        class="badge"
        numberType="limit"
        type="warning"
        max="99"
        :value="outlineCount"
      ></wd-badge>
      <u-tag text="暂存列表" type="primary" @click="toCacheDataList"></u-tag>
    </view> -->
  </view>
</template>
<script lang="ts" setup>
import { useBasePage } from '@/pages/useBasePage'
import FillListContent from './components/FillListContent/index.vue'
import TaskListContent from './components/TaskListContent/index.vue'
import { useStorageDataStore } from '@/store'
import _ from 'lodash'
const { navigateBack } = useBasePage()

const PAGE_PARAM = {
  pageNum: 1,
  pageSize: 10,
}

const useStorageData = useStorageDataStore()
let formUid
let fillFormUid
const title = ref()
const cardConfig = ref()
const searchConfig = ref()
const fillCardConfig = ref()
const fillSearchConfig = ref()
const tab = ref(0)

onLoad(async (e) => {
  formUid = e.formUid
  title.value = e.title
  const conf = useStorageData.getStorageData(formUid)
  console.log('conf :>> ', conf)
  if (conf.card_config) {
    cardConfig.value = JSON.parse(conf.card_config)
  }
  if (conf.search_config) {
    searchConfig.value = {
      adminFilter: {
        defaultLabel: '自治区',
        adminConfigs: {
          province: {
            url: 'admin_province',
            next: 'city',
            level: 'province',
            field: 'pcode',
          },
          city: {
            url: 'admin_city',
            next: 'county',
            valuekey: 'ccode',
            labelkey: 'cname',
            level: 'city',
            field: 'ccode',
          },
          county: {
            url: 'admin_county',
            next: 'town',
            valuekey: 'fcode',
            labelkey: 'fname',
            filterField: 'ccode',
            level: 'county',
            field: 'fcode',
          },
          town: {
            url: 'admin_town',
            next: 'village',
            valuekey: 'tcode',
            labelkey: 'tname',
            filterField: 'fcode',
            level: 'town',
            field: 'tcode',
          },
          village: {
            url: 'admin_village',
            valuekey: 'xcode',
            labelkey: 'xname',
            filterField: 'tcode',
            level: 'village',
            field: 'xcode',
          },
        },
      },
      searchFilter: {
        value: null,
        label: '数据id',
        field: '_id',
      },
      selectFilters: [
        {
          label: '类型',
          field: 'field1',
          value: null,
          options: [
            { label: '全部', value: null },
            { label: '新款商品', value: 1 },
            { label: '活动商品', value: 2 },
          ],
        },
        {
          label: '状态',
          field: 'field2',
          value: null,
          options: [
            { label: '全部', value: null },
            { label: '已退回', value: 1 },
            { label: '已填报', value: 2 },
          ],
        },
      ],
    }
  }
  if (conf.fill_form_name) {
    fillFormUid = conf.fill_form_name
  }
  if (conf.fill_card_config) {
    fillCardConfig.value = JSON.parse(conf.fill_card_config)
  }
  // await this.getCacheData()
})

// export default {
//   components: { FillListContent, TaskListContent },
//   data() {
//     return {
//       formUid: null,
//       title: '',
//       page: PAGE_PARAM,
//       dataList: [],
//       inputValue: null,
//       filter: null,
//       containerHeight: 0,
//       total: 0,
//       outlineCount: 0,
//       filtersMap: {},
//       sectionList: ['待填报', '已填报'],
//       currentSection: 0,
//       fillFormUid: null,
//       fillCardConfig: null,
//     }
//   },
//   async onLoad(e) {
//     this.formUid = e.formUid
//     this.title = e.title
//     const conf = Storage.getFormData(this.formUid)
//     console.log('conf :>> ', conf)
//     if (conf.card_config) {
//       this.cardConfig = JSON.parse(conf.card_config)
//     }
//     if (conf.search_config) {
//       this.searchConfig = JSON.parse(conf.search_config)
//     }
//     if (conf.fill_form_name) {
//       this.fillFormUid = conf.fill_form_name
//     }
//     if (conf.fill_card_config) {
//       this.fillCardConfig = JSON.parse(conf.fill_card_config)
//     }
//     await this.getCacheData()
//   },
//   async onShow() {
//     if (this.formUid) {
//       await this.getCacheData()
//       if (this.formUid) {
//         this.$nextTick(() => {
//           if (this.$refs.fillListContent) {
//             this.$refs.fillListContent.reloadData()
//           }
//           if (this.$refs.taskListContent) {
//             this.$refs.taskListContent.reloadData()
//           }
//         })
//       }
//     }
//   },
//   methods: {
//     sectionChagneHandler(index) {
//       this.currentSection = index
//     },
//     async getCacheData() {
//       const res = await this.$apis.formDataLocal.countCacheRecords(this.fillFormUid)
//       console.log(res, 'cache')
//       this.outlineCount = res
//     },
//     toCacheDataList() {
//       Storage.saveFormData(this.fillFormUid, this.fillCardConfig)
//       uni.navigateTo({
//         url: `/app/pages/CacheDataListPage/index?formUid=${this.fillFormUid}&taskFormUid=${this.formUid}`,
//       })
//     },
//     fillHandler(e) {
//       console.log(e, 'fillHandler')
//       Storage.saveFormData(
//         e._id,
//         _.omit(e, ['_id', 'update_time', 'update_by', 'create_by', 'create_time', 'state']),
//       )
//       uni.navigateTo({
//         url: `/app/pages/FillFormPage/index?formUid=${this.fillFormUid}&task_id=${e._id}&taskFormUid=${this.formUid}`,
//       })
//     },
//   },
// }
</script>

<style lang="scss" scoped>
.offline-list {
  position: absolute;
  right: 30rpx;
  bottom: 120rpx;
  z-index: 10000;
  display: block;
  cursor: pointer;
  box-shadow: 2px 2px 3px rgba(0, 0, 0, 0.2);
  .badge {
    position: absolute;
    top: -10rpx;
    right: -20rpx;
    z-index: 99;
  }
}
</style>
