{
  "easycom": {
    "^u-(.*)": "uview-ui/components/u-$1/u-$1.vue"
  },
  "pages": [
    {
      "path": "pages/Workspace/index"
    },
    {
      "path": "pages/User/index"
    },
    {
      "path": "pages/FormList/index"
    },
    {
      "path": "pages/FormPage/index"
    },
    {
      "path": "pages/Login/index"
    },
    {
      "path": "pages/ViewPage/index"
    },
    {
      "path": "components/common/SubFormPage/index"
    }
  ],
  "subPackages": [
    {
      "root": "pages_plowland",
      "pages": [
        {
          "path": "pages/index/index"
        },
        {
          "path": "pages/Verify/PlotInspect/index"
        },
        {
          "path": "pages/Verify/InspectRecord/index"
        },
        {
          "path": "pages/Verify/FillSchedule/index"
        },
        {
          "path": "pages/UpdatePage/index"
        },
        {
          "path": "pages/Verify/index"
        },
        {
          "path": "pages/Project/index"
        },
        {
          "path": "pages/ProjectList/index"
        },
        {
          "path": "pages/SelfFormPage/index"
        },
        {
          "path": "pages/Query/index"
        }
      ]
    },
    {
      "root": "pages_farmland",
      "pages": [
        {
          "path": "pages/index/index"
        },
        {
          "path": "pages/workspaceMenu/workspaceMenu"
        },
        {
          "path": "pages/pointListPage/pointListPage"
        },
        {
          "path": "pages/pointMapPage/pointMapPage"
        }
      ]
    }
  ],
  "tabBar": {
    "selectedColor": "#00C56E",
    "list": [
      {
        "selectedIconPath": "static/images/workspace-active.png",
        "iconPath": "static/images/workspace.png",
        "pagePath": "pages/Workspace/index",
        "text": "工作台"
      },
      {
        "selectedIconPath": "static/images/user-active.png",
        "iconPath": "static/images/user.png",
        "pagePath": "pages/User/index",
        "text": "我的"
      }
    ]
  },
  "globalStyle": {
    "navigationStyle": "custom",
    "navigationBarTextStyle": "white",
    "navigationBarBackgroundColor": "#ffffff",
    "backgroundColor": "#ffffff",
    "app-plus": {
      "bounce": "none",
      "scrollIndicator": "none" //全局 在APP页面都不显示滚动条
    }
  }
}
