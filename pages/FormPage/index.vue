<template>
  <view>
    <u-navbar title="数据填报" @leftClick="leftClickHandler" :bgColor="$constants.COLOR.PRIMARY_COLOR" placeholder></u-navbar>
    <view class="content">
      <GTForm ref="gtForm" v-if="options" :options="options" @initialized="initHandler" @submit="submitHandler"></GTForm>
    </view>
    <view class="btn-list" v-if="!readonly">
      <u-button text="暂 存" type="primary" v-if="cacheable" @click="saveLocalHandler"> </u-button>
      <u-button text="立即提交" type="primary" @click="commitHandler"> </u-button>
    </view>
    <u-modal
      :show="modalVisible"
      :title="$constants.MSG.WARNING_TITLE"
      :content="$constants.MSG.WARNING_DATA_SAVE"
      :confirmColor="$constants.COLOR.PRIMARY_COLOR"
      showCancelButton
      @cancel="modalCancelHandler"
      @confirm="modalConfirmHandler"
    ></u-modal>
  </view>
</template>

<script>
import BasePage from "@/pages/BasePage";
import GTForm from "@/components/form/GTForm";
import Storage from "@/tools/Storage";
import ChangeDetector from "@/tools/ChangeDetector";

export default {
  mixins: [BasePage],

  components: {
    GTForm,
  },

  data() {
    return {
      options: undefined,
      formUid: undefined,
      formDef: undefined,
      formRecordData: undefined,
      cacheRecordId: undefined,
      cacheable: true,
      readonly: false,
      changeDetector: undefined,
      modalVisible: false,
    };
  },

  onLoad(option) {
    this.formUid = option.formUid;
    this.formRecordUid = option.formRecordUid;
    this.cacheRecordId = option.cacheRecordId;
    this.cacheable = option.cacheable !== "false";
    this.readonly = option.readonly ? true : false;
    this.initForm();
  },

  // 响应手机物理或者虚拟返回按键
  onBackPress(e) {
    if (e.from === "backbutton") {
      this.leftClickHandler();
      return true;
    }
  },

  methods: {
    async initForm() {
      let options = {};
      if (this.formUid) {
        // 获取表单定义
        options.formUid = this.formUid;
        options.formDef = await this.$apis.formDef.getFormDef(this.formUid);
      }
      if (this.formRecordUid) {
        // 获取表单数据
        options.formRecordUid = this.formRecordUid;
        const result = Storage.getFormData(this.formRecordUid);
        options.formRecordData = this.$utils.form.toFrontFormData(result);
      }
      if (this.cacheRecordId) {
        const result = Storage.getFormData(this.cacheRecordId);
        options.formRecordData = this.$utils.form.toFrontFormData(result);
      }
      if (e.readonly === "1") {
        this.readonly = true;
        options.readonly = true;
      }
      //
      this.options = options;
    },

    initHandler(formData) {
      this.changeDetector = new ChangeDetector(formData);
    },

    // 返回到上一页
    leftClickHandler() {
      const formData = this.$refs.gtForm.getFormData();
      const change = this.changeDetector.detect(formData);
      if (change) {
        this.modalVisible = true;
      } else {
        this.navigateBack();
      }
    },

    async saveLocalHandler() {
      const formData = this.$refs.gtForm.getFormData();
      await this.saveLocal(formData);
    },

    commitHandler() {
      this.$refs.gtForm.submit();
    },

    async submitHandler(data) {
      await this.submitData(data);
    },

    // 本地存储
    async saveLocal(formData) {
      if (!formData) return;
      const cacheRecordId = await this.$apis.formDataLocal.upsertCacheRecord(this.formUid, this.cacheRecordId, formData);
      if (cacheRecordId) {
        this.cacheRecordId = cacheRecordId;
        this.showSuccess(this.$constants.MSG.DATA_CACHED_SUCCESS);
        // 重置变化检测
        this.changeDetector.setSource(formData);
      } else {
        this.showError(this.$constants.MSG.DATA_CACHED_FAIL);
      }
    },

    // 向服务器端提交数据
    async submitData(data) {
      if (!data) return;
      this.showLoading(this.$constants.MSG.DATA_SUBMITTING);
      const formData = this.$utils.form.toApiFormData(data);
      if (formData._id) {
        await this.updateFormData(formData);
      } else {
        await this.addFormData(formData);
      }
    },

    // 更新表单数据
    async updateFormData(formData) {
      try {
        const result = await this.$apis.formData.updateFormRecord(this.formUid, formData._id, formData, this.cacheRecordId);
        this.showSuccess(this.$constants.MSG.DATA_SUBMIT_SUCCESS);
        this.navigateBackWithNoCheck();
      } catch (err) {
        // this.showError(this.$constants.MSG.DATA_SUBMIT_FAIL);
        uni.$u.toast(err);
      } finally {
        uni.hideLoading();
      }
    },

    // 增加表单数据
    async addFormData(formData) {
      try {
        const result = await this.$apis.formData.addFormRecord(this.formUid, formData, this.cacheRecordId);
        this.showSuccess(this.$constants.MSG.DATA_SUBMIT_SUCCESS);
        this.navigateBackWithNoCheck();
      } catch (err) {
        // this.showError(this.$constants.MSG.DATA_SUBMIT_FAIL);
        uni.$u.toast(err);
      } finally {
        uni.hideLoading();
      }
    },

    modalCancelHandler() {
      this.modalVisible = false;
    },

    modalConfirmHandler() {
      this.navigateBackWithNoCheck();
      this.modalVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/styles/common.scss";

.content {
  padding: 0px 20rpx 64px;
}

/deep/ .u-navbar__content {
  .u-navbar__content__title,
  .u-icon__icon {
    color: $gt-navbar-title-color !important;
  }
}

.btn-list {
  box-sizing: border-box;
  z-index: 999;
  display: flex;
  columns: 2;
  gap: 24rpx;
  padding: 24rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #fff;
}
</style>
